module Api::V1::StoreHelper
  # Max price constant for price range
  MAX_PRICE_PER_PRODUCT = Design::MAX_PRICE
  COPYRIGHT_TERMS = COPYRIGHT_TERMS.split(',')
  ADDITIONAL_OPTIONS = [:property_value_ids, :category_child_ids, :designer_ids, :option_type_value_ids, :min_discount,
                        :max_discount, :min_price, :max_price, :min_carat, :max_carat, :min_rating, :pincode, :max_odr,
                        :sort, :gender]

  # Converting given value according to rate and ceiling it
  #
  # == Parameters:
  # value::
  #   Integer
  # rate::
  #   Integer
  #
  # == Returns:
  #   Float
  #

  SORT =     {
    '1' => { name: 'Top Rated', attribute: :graded_rating, order: :desc },
    '2' => { name: 'Price - Low to High', attribute: :discount_price, order: :asc },
    '3' => { name: 'Price - High to Low', attribute: :discount_price, order: :desc },
    '4' => { name: 'Newest First', attribute: :created_at, order: :desc },
    '5' => { name: 'Discounts', attribute: :discount_percent, order: :desc },
    '6' => { name: 'Popularity', attribute: :sell_count, order: :desc },
    '8' => { name: 'Trending', attribute: :clicks_impression, order: :desc },
    '9' => { name: 'Most Ordered', attribute: :trending_designs, order: :desc },
    '10' => { name: 'Best Top Rated', attribute: :dom_grade_mob, order: :desc },
    '11' => { name: 'Recommended', attribute: :int_grade_mob, order: :desc },
    '12' => { name: 'eid', attribute: :category_grade, order: :desc },
    '14' => { name: 'Recently Popular', attribute: :sell_count_30, order: :desc}
    }.freeze

  DOMESTIC_SORT =     {
    '7' => { name: 'HandPicked', attribute: :grade, order: :desc },
    '13' => { name: 'Category', attribute: :dom_grade_mob, order: :desc }
  }.freeze
  INTERNATIONAL_SORT = {
    '7' => { name: 'HandPicked', attribute: :international_grade, order: :desc },
    '13' => { name: 'Category', attribute: :int_grade_mob, order: :desc }
  }.freeze


  def self.currency(value, rate)
    (value / rate).ceil
  end

  # Build View Equivalent of Price Range for Country Currencies
  #
  # == Returns::
  #   Hash
  #
  def self.price_range_currency
    params = {
      1..500 => {}, 501..1000 => {}, 1001..2000 => {},
      2001..4000 => {}, 4001..MAX_PRICE_PER_PRODUCT => {},
      0..MAX_PRICE_PER_PRODUCT => {}
    }
    currencies = CurrencyConvert.currency_convert_memcached
    params.each do |range, value|
      currencies.each do |currency|
        start_range = self.currency(range.first, currency.rate)
        end_range = self.currency(range.last, currency.rate)
        params[range][currency.country_code] = "#{start_range} - #{end_range}"
      end
    end
    params
  end

  def create_url_parameters(facet_properties, other_options)
    url = ''
    if other_options.present?
      # Add all these additional options to URL
      ADDITIONAL_OPTIONS.each do |option|
        option_value = other_options[option]
        if option_value.present?
          case option
          when :sort
            url += "&#{option.to_s}=#{Api::V1::StoreController::SORT.key(option_value.to_s.downcase)}"
          when :max_carat, :min_carat, :pincode
            url += "&#{option.to_s}=#{option_value.to_s}"
          when :property_value_ids, :category_ids, :designer_ids, :option_type_value_ids, :category_child_ids
            option_value = option_value.split(',').map(&:to_i)
            if option == :property_value_ids
              option_value -= facet_properties.collect { |property| property[:id] }
            end
            url += "&#{option.to_s}=#{option_value.join(',')}" unless option_value.empty?
          when :max_odr
            url += "&#{option.to_s}=#{option_value.to_s}" unless option_value == -1
          when :min_rating
            url += "&#{option.to_s}=#{option_value.to_s}" unless option_value == 0
          when :max_price, :min_price
            unless (other_options[:min_price] == 0 && other_options[:max_price] == MAX_PRICE_PER_PRODUCT)
              url += "&#{option.to_s}=#{option_value.to_s}"
            end
          when :max_discount, :min_discount
            unless (other_options[:min_discount] == 0 && other_options[:max_discount] == 100)
              url += "&#{option.to_s}=#{option_value.to_s}"
            end
          when :gender
            url += "&#{option.to_s}=#{option_value.to_s}"
          end
        end
      end
      #url[0] = '?' unless url.empty?
    end
    url
  end

  def create_faceted_url(kind, new_property, facet_properties, current_url)
    url = current_url.dup
    if kind && FACETED_URL_KINDS[kind] && new_property.present?
      property_values = facet_properties.inject([new_property]) do |property_values, value|
        if value[:name] != new_property[:name]
          property_values.push(value)
        end
        property_values
      end
      property_values.sort! do |x, y|
        if x[:priority].abs == y[:priority].abs
          x[:name] <=> y[:name]
        else
          x[:priority].abs <=> y[:priority].abs
        end
      end
      #url += '/' unless url.last == '/'
      url += "#{property_values.map!{|pv| pv[:name]}.join('_')}-#{FACETED_URL_KINDS[kind]['name']}"
    end
    url
  end

  def create_url_for_facets?(type, facet_properties)
    if type == 'checkbox'
      !facet_properties.any?{|prop| prop[:type] == 'checkbox'} && facet_properties.length < 2
    else
      facet_properties.length < 2
    end
  end

  def grading_attribute(type)
    if mobile_attribute?
      type.eql?('domestic') ? :grade_mobile : :international_grade_mobile
    else
      type.eql?('domestic') ? :grade : :international_grade
    end
  end

  def mobile_attribute?
    User.app_source.eql?('mobile') && Design.app_specific_grading? #&& CATEGORY_MOBILE_GRADE.include?(params[:category_parent_id].to_s)
  end

  # Price Facet Constant
  PRICE_RANGE = send(:price_range_currency)

  # Sort options with keys: [attribute, order]

  PIPELINE_SORT = {
    '2' => [
      { attribute: :discount_price_bucket, order: :asc },
      { attribute: :sell_count_bucket, order: :desc },
      { attribute: :grade_bucket, order: :desc }
    ],
    '3' => [
      { attribute: :discount_price_bucket, order: :desc },
      { attribute: :sell_count_bucket, order: :desc },
      { attribute: :grade_bucket, order: :desc }
    ],
    '4' => [
      { attribute: :created_at_bucket, order: :desc },
      { attribute: :sell_count_bucket, order: :desc },
      { attribute: :clicks_bucket, order: :desc }
    ]
  }.freeze

  PIPELINE_SORT_MOBILE = {
    '2' => [
      { attribute: :discount_price_bucket, order: :asc },
      { attribute: :sell_count_bucket, order: :desc },
      { attribute: :grade_bucket_mobile, order: :desc }
    ],
    '3' => [
      { attribute: :discount_price_bucket, order: :desc },
      { attribute: :sell_count_bucket, order: :desc },
      { attribute: :grade_bucket_mobile, order: :desc }
    ],
    '4' => [
      { attribute: :created_at_bucket, order: :desc },
      { attribute: :sell_count_bucket, order: :desc },
      { attribute: :clicks_bucket, order: :desc }
    ]
  }.freeze

  def self.domestic_sorts
    SORT.merge(DOMESTIC_SORT)
  end

  def self.international_sorts
    SORT.merge(INTERNATIONAL_SORT)
  end

  def domestic_sorts
    RequestStore.cache_fetch("mobile_sorts_domestic_#{MirrawUtil.platform}"){
      Api::V1::StoreHelper.domestic_sorts.merge({ '7' =>  { name: 'HandPicked', attribute: grading_attribute('domestic'), order: :desc }})
    }
  end

  def international_sorts
    RequestStore.cache_fetch("mobile_sorts_international_#{MirrawUtil.platform}"){
      Api::V1::StoreHelper.international_sorts.merge({ '7' =>  { name: 'HandPicked', attribute: grading_attribute('international'), order: :desc }})
    }
  end

  # Todo EXPLAIN CONSTANT
  FILTERS = {
    id: [
      {
        name: 'Category', solr_key: :category_ids, opt_key: :category_child_ids,
        name_key: :p_name, relational: false, solr_with_grp: false, position: CATEGORY_FILTER_POSITION
      },
      {
        name: 'Designer', solr_key: :designer_id, opt_key: :designer_ids,
        name_key: :p_name, relational: false, solr_with_grp: true, position: DESIGNER_FILTER_POSITION
      },
      {
        name: 'PropertyValue', solr_key: :property_value_ids,
        opt_key: :property_value_ids, name_key: :p_name, relational: true,
        relation: :property, group_key: :property_id, solr_with_grp: false, position: PROPERTY_VALUE_FILTER_POSITION
      },
      {
        name: 'OptionTypeValue', solr_key: :option_type_value_ids,
        opt_key: :option_type_value_ids, name_key: :p_name, relational: true,
        relation: :option_type, group_key: :option_type_id, solr_with_grp: true, position: OPTION_TYPE_VALUE_FILTER_POSITION
      }
    ],
    range: [
      {
        name: 'Price', solr_key: :discount_price, opt_key: { min: :min_price, max: :max_price },
        currency_convert: true, max: MAX_PRICE_PER_PRODUCT, solr_with_grp: true, position: PRICE_FILTER_POSITION
      },
      {
        name: 'Discount Percent', solr_key: :discount_percent, opt_key: { min: :min_discount, max: :max_discount },
        currency_convert: false, max: 100, solr_with_grp: false, position: DISCOUNT_PERCENT_FILTER_POSITION
      },
      {
        name: 'Rating', solr_key: :average_rating, opt_key: {min: :min_rating, max: :max_rating},
        currency_convert: false, max: 5, solr_with_grp: true, position: RATING_FILTER_POSITION
      }
    ]
  }.freeze

  def self.discount_promotion
    PromotionPipeLine.global_sale_discount_promotion
  end

  def self.category_wise_discount_promotion
    PromotionPipeLine.category_wise_discount_promotion
  end

  # Get sale factor for global sale promotion
  #
  # == Returns:
  # Float
  #
  def self.get_sale_factor
    value = 100
    if global_sale?
      var_hash = discount_promotion.variables_hash
      value -= JSON.parse(var_hash)['global_discount_percent'].to_f
    end
    100 / value.to_f
  end

  # Check currently global sale promotion is running or not
  #
  # == Returns:
  # Boolean
  #
  def self.global_sale?
    if (globle_sale = discount_promotion).present?
      Time.zone.now.to_i.between?(Time.parse(globle_sale.start_date.to_s).to_i,
      Time.parse(globle_sale.end_date.to_s).to_i)
    else
      false
    end
  end

  def global_sale_in_country?
    if (global_sale = PromotionPipeLine.global_sale_discount_promotion).present?
      (Time.zone.now.to_i.between?(Time.parse(global_sale.start_date.to_s).to_i, Time.parse(global_sale.end_date.to_s).to_i)) && (global_sale.country_code.include?(Design.country_code) || global_sale.country_code.blank?)
    else
      false
    end
  end

  def global_sale_discount_percent
    value = 100.0
    if (global_sale = PromotionPipeLine.global_sale_discount_promotion)
      var_hash = global_sale.variables_hash
      value = JSON.parse(var_hash)['global_discount_percent'].to_f
    end
    value
  end

  SALE_FACTOR = send(:get_sale_factor)

  # Gets search results from solr
  #
  # == Parameters:
  # opt::
  #   Expects a hash containing values - state, in_catalog_one,
  #   discount_percent, discount_price, category_child_ids, designer_ids,
  #   option_type_value_ids, property_value_ids, category_parent_id, tag
  #
  # == Returns:
  # Sunspot Search Object
  #
  def solr_results(opt)
    opt = fix_values(opt)
    @option_types = opt[:category_parent_id] > 0 ?
                    fetch_option_types(Category.find_by_id(opt[:category_parent_id])) :
                    nil
    @properties = fetch_properties(Category.find_by_id(opt[:category_parent_id]))
    solr_search(opt)
  end

  # Fetches unique option type names (`p_name`) for a given category.
  # - If the category has its own option types, return those.
  # - If it has none, and a parent is present in FILTER_PARENT_CATEGORIES,
  #   then fallback to that parent's option types.
  def fetch_option_types(category)
    return [] unless category

    option_types = category.option_types.pluck(:p_name)
    return option_types.uniq if option_types.any?

    # Only fallback to parent if current category has no option_types
    matching_parent = category.ancestors.detect { |c| FILTER_PARENT_CATEGORIES.include?(c.name.downcase) }
    return matching_parent ? matching_parent.option_types.pluck(:p_name).uniq : []
  end
 

  def fetch_properties(category)
    return [] unless category

    if FILTER_PARENT_CATEGORIES.include?(category.name.downcase)
      category.properties.pluck(:p_name).uniq
    elsif category.parent
      category.parent.properties.pluck(:p_name).uniq
    else
      []
    end
  end

  # Does required fields parsing / adjustment / type casting
  # Required before passing to solr
  #
  # == Parameters:
  # opt::
  #   Expects a hash containing min_price, max_price, min_discount, max_discount,
  #   sort, state, category_parent_id
  #
  # == Returns:
  # Hash
  #

  def fix_values(opt)
    FILTERS[:id].each do |attr_opt|
      key = attr_opt[:opt_key]
      if opt[key].present?
        opt[key] = opt[key].split(',').map(&:to_i)
        opt[:grouped_cluster] = 1 if attr_opt[:solr_with_grp]
      end
    end
    opt[:colour_property_value_ids] = opt[:colour_property_value_ids].split(',').map(&:to_i) if opt[:colour_property_value_ids].present?
    opt[:category_parent_id] = opt[:category_parent_id].to_i
    opt[:designer_id] = opt[:designer_id].to_i
    if opt[:category_grade].present?
      opt[:sort] = '11'
    elsif CATEGORY_SET.include? opt[:category_parent_id] 
      opt[:sort] = '10'
    elsif opt[:category_name].blank? && opt[:designer_name].blank? && opt[:sort].blank?
      opt[:sort] = '7'
    end

    opt[:sort_pipeline] = mobile_attribute? ? PIPELINE_SORT_MOBILE[opt[:sort]] : PIPELINE_SORT[opt[:sort]] if ENABLE_PIPELINE_SORT
    if opt[:sort].present?

      opt[:sort] = @country_code == 'IN' ? domestic_sorts[opt[:sort]] : international_sorts[opt[:sort]]
    end

    if opt[:pincode].present?
      opt[:cod_shipper_ids] = Courier.cod_shipper_ids(opt[:pincode].split(',')) << -1
    end

    if (preference = opt[:preference]).present?
      opt[:preference] = preference.is_a?(SolrPreferenceQuery) ? preference : SolrPreferenceQuery.new(preference)
    end
    opt[:state] = opt[:state] || 'in_stock'

    fix_min_max_values(opt)
  end

  # Does required fields parsing / adjustment / type casting
  #
  # == Parameters:
  # opt::
  #   Expects a hash containing min_price, max_price, min_discount, max_discount,
  #
  # == Returns:
  # Hash
  #
  def fix_min_max_values(opt)
    FILTERS[:range].each do |value|
      min_key = value[:opt_key][:min]
      max_key = value[:opt_key][:max]
      opt[:grouped_cluster] = 1 if value[:solr_with_grp] && (opt[min_key].present? || opt[max_key].present?)
      min, max = opt[min_key].to_i, opt[max_key].to_i
      max = value[:max] if max.zero? || max < min || max > value[:max]
      min = 0 unless min < max
      opt[max_key] = max
      opt[min_key] = min
    end
    opt
  end

  # Gets search results from solr based on param passed
  #
  # == Parameters:
  # opt::
  #   Expects a hash containing values - state, in_catalog_one,
  #   discount_percent, discount_price, category_child_ids, designer_ids,
  #   option_type_value_ids, property_value_ids, category_parent_id, tag
  #
  # == Returns:
  # Sunspot Search Object
  #
  def solr_search(opt)
    if (opt[:kind] == 'b1g1' || opt[:buy_get_free] == '1') && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && bmgnx_hash[:buy_get_free] == 1
      opt[:max_price] = bmgnx_hash[:filter].to_i
      opt[:buy_get_free] = nil
    end
    opt[:grouped_cluster] ||= 0
    preload = [:designer, :master_image, :categories, :variants]
    preload.push(:dynamic_price_for_current_country) if DYNAMIC_PRICE_ENABLED && Design.country_code != 'IN'
    # preload.push(:categories) if Promotion.discount_offer_on_category(PromotionPipeLine.active_promotions).present?
    results = Design.search(include: preload) do
      with(:state, opt[:state])
      without(:in_catalog_one, opt[:in_catalog_one] || 0)
      without(:in_catalog_one, 3)
      if Design.country_code !='IN'
        without(:in_catalog_one,1)
      else
        without(:in_catalog_one,2)
      end
      if opt[:copyright_filter].present?
        fulltext get_copyright_terms
      end
      if opt[:buy_get_free].present? && PromotionPipeLine.bmgnx_hash.present?
        with(:buy_get_free, 1)
      elsif opt[:buy_get_free].present? && opt[:kind] == "direct_dollar" && ['inr', 'rs'].exclude?(opt['Currency-Code'].try(:downcase))
        with(:buy_get_free, 4)
      end
      with(:premium, true) if opt[:premium]
      with(:designer_id, opt[:designer_id]) if opt[:designer_id] > 0

      opt[:min_discount] = adjust_promo_discount(opt[:min_discount].to_i)
      opt[:max_discount] = adjust_promo_discount(opt[:max_discount].to_i)

      facet :discount_percent, range: 0..100, range_interval: 10,
        exclude: with(:discount_percent, opt[:min_discount]..opt[:max_discount]), include: 'all'

      facet :discount_price, exclude: with(:discount_price,
        (opt[:min_price] * SALE_FACTOR)..(opt[:max_price] * SALE_FACTOR)) do
        PRICE_RANGE.keys.each do |range|
          row(range) { with(:discount_price,
            (range.begin * SALE_FACTOR)..(range.end * SALE_FACTOR)
          )}
        end
      end
      with(:ready_to_ship, true) if opt[:ready_to_ship].present?
      facet :ready_to_ship

      if opt[:min_rating]<= 0 && opt[:max_rating]>= 5
        facet :average_rating, range: 0..6, range_interval: 1
      else
        facet :average_rating, range: 0..6, range_interval: 1,
          exclude: with(:average_rating, opt[:min_rating]..opt[:max_rating])
      end

      # designer odr facet for designs
      odr_90 = opt[:max_odr] || -1
      if ((odr_90 = odr_90.to_f/100) >= 0.0)
        facet :odr_90_day, include: with(:odr_90_day, 0..odr_90)
        opt[:grouped_cluster] = 1
      else
        facet :odr_90_day
      end

      preference_query = opt[:preference]

      FILTERS[:id].each do |attr_opt|
        solr_key = attr_opt[:solr_key]
        values = opt[attr_opt[:opt_key]]

        # Set a higher limit for property values as there can be many, but no limit for others.
        # A limit of -1 means unlimited. You can adjust the 200 value if needed.
        facet_limit = (attr_opt[:solr_key] == :property_value_ids) ? 200 : -1

        if values.present? && values.count > 0
          # UNIFIED LOGIC: This now applies to Categories, Designers, AND PropertyValues.
          # It excludes all currently selected values for this specific filter group when
          # calculating the facet counts. This allows users to easily switch between
          # options in the same filter without the counts disappearing.
          facet solr_key, exclude: with(solr_key, values), limit: facet_limit
        else
          # If no filters are applied for this group, just get the standard facet counts.
          facet solr_key, limit: facet_limit
        end
      end

      if opt[:created_at].present?
        with(:created_at).greater_than(opt[:created_at].to_i.days.ago)
      end

      if opt[:grade_name].present? && opt[:sort_pipeline].blank?
        order_by(opt[:grade_name].to_sym, opt[:order_seq])
      end

      if opt[:category_parent_id] > 0
        with(:category_parents, opt[:category_parent_id])
      elsif opt[:tag].present?
        keywords opt[:tag], fields: :tag_list
      elsif opt[:term].present?
        all do
          if opt[:category_name].present?
            fulltext(opt[:category_name], fields: :category_name)
          elsif opt[:designer_name].present?
            fulltext(opt[:designer_name], fields: :designer_name)
          end
          search_term_length = opt[:term].split(' ').count(&:present?)
          fulltext opt[:term], fields: %i[category_name title product_id specification keywords imp_keywords] do
            search_keyword_boosting = SearchKeywordBoosting.dup
            if opt[:sort].present? && %i[grade international_grade international_grade_mobile grade_mobile].exclude?(opt[:sort][:attribute])
              boost(function{scale(opt[:sort][:order] == :asc ? product(opt[:sort][:attribute],-1) : opt[:sort][:attribute],1,search_keyword_boosting.delete(:ordering_boost_scale) || 2)})
            elsif opt[:sort].present? && opt[:sort][:attribute] == :discount_price
              boost(function{scale(opt[:sort][:order] == :asc ? product(opt[:sort][:attribute],-1) : opt[:sort][:attribute],1,search_keyword_boosting.delete(:discount_price_boost_scale) || 8)})
            else
              boost(function{scale(:sell_count,search_keyword_boosting.delete(:min_sell_count_scale) || 1,search_keyword_boosting.delete(:max_sell_count_scale) || 1.4)})
            end
            if search_term_length >= 8
              minimum_match 6
            elsif search_term_length > 4
              minimum_match(search_term_length / 2 + 2)
            end
            boost_fields search_keyword_boosting
            if preference_query.present?
              preference_query.execute(self)
            end
          end
          order_by('score','desc')
        end
      elsif opt[:designer_id] > 0
        opt[:grouped_cluster] = -1
        with(:designer_id, opt[:designer_id])
      elsif opt[:collection].present?
        with(:collection_list_exact, opt[:collection])
        if preference_query.present?
          preference_query.execute(self)
          order_by('score','desc')
        end
      elsif opt[:catalogue].present?
        with(:catalogue_list_exact, opt[:catalogue])
        if preference_query.present?
          preference_query.execute(self)
          order_by('score','desc')
        end
      elsif opt[:autosuggest_term].present?
        fulltext params[:autosuggest_term] do
          phrase_fields title: 5.0, designer_name: 4.0, category_name: 3.0, description: 2.0
        end
      end
      text_search_query = opt[:term].present? || opt[:collection].present? || opt[:catalogue].present?
      if preference_query.present? && !text_search_query
        fulltext '*:*' do
          preference_query.execute(self)
        end
        order_by(:score, :desc)
      end
      if Design::CLUSTER_CATEGORY_WISE_IDS == 0 || (opt[:category_parent_id].present? && Design::CLUSTER_CATEGORY_WISE_IDS.include?(opt[:category_parent_id]))
        if opt[:grouped_cluster] == 0 || [6, 7].include?(opt[:category_parent_id].to_i) || opt[:term].present?
          with(:cluster_winner, 1)
        elsif opt[:grouped_cluster] == 1
          group :cluster_id_str do
            simple
            order_by(:design_cluster_score, :desc)
          end
        end
      end

      with(:cod_shipper_ids, opt[:cod_shipper_ids]) if opt[:cod_shipper_ids].present?
      if opt[:page].to_i <= 0 then opt[:page] = 1 end
      # Increased default from 22 to 36 for better infinite scroll performance
      paginate page: opt[:page], per_page: opt[:items_per_page] || ((per_page_count = DESIGN_PER_CATALOG_PAGE.to_i).zero? ? 36 : per_page_count)
      unless opt[:sort].blank? || opt[:autosuggest_term].present?
        if opt[:sort_pipeline].present?
          opt[:sort_pipeline].each do |sort_pipeline|
            order_by(sort_pipeline[:attribute],sort_pipeline[:order])
          end
        end
        order_by(opt[:sort][:attribute], opt[:sort][:order])
        # if opt[:sort][:attribute] == :graded_rating
        #   order_by(:average_rating, :desc)
        #   order_by(:review_count, :desc)
        # else
        # end
      end
    end
    results
  end

  # Gets filter details
  #
  # == Parameters:
  # filter::
  #   A hash containing based on Filters Constant
  #
  # == Returns:
  # Hash
  #
  def filter_values(filter, object, type)
    params = nil
    if (facet = object.facet(filter[:solr_key])).present?
      case type
      when :range
        params = filter_values_range(facet, filter)
      when :id
        params = filter_values_id(facet, filter)
      end
    end
    params
  end

  # Gets value for id filters
  #
  # == Parameters:
  # facet::
  #   Sunspot Search Facet Object
  # filter
  #   Hash - With Keys as per FILTER ARRAY CONSTANT
  #
  # == Returns
  #   Hash
  #
  def filter_values_id(facet, filter)
    solr = {counts: {}, values: []}
    series = {}
    i = 0
    facet.rows.each do |f|
      solr[:counts][f.value] = f.count
      series[f.value] = i
      i -= 1
    end
    solr[:values] = filter[:name].constantize.where(id: solr[:counts].keys).
      order(filter[:group_key])
    if filter[:relation].present?
      solr[:values] = solr[:values].includes(filter[:relation]).sort_by{|obj| series[obj.id].to_i }
    end
    if filter[:group_key]
      solr[:values] = solr[:values].group_by{|f| f[filter[:group_key]]}
    end
    solr
  end

  # Gets value for range filters
  #
  # == Parameters:
  # facet::
  #   Sunspot Search Facet Object
  # filter
  #   Hash - With Keys as per FILTER ARRAY CONSTANT
  #
  # == Returns
  #   Hash
  #
  def filter_values_range(facet, filter)
    items = []
    count = facet.rows.map{ |f| f.count }.inject(:+) if facet.try(:field_name).to_s == 'average_rating'
    facet.rows.each do |f|
      item = {values: {min: f.value.first, max: f.value.last}, count: f.count}
      item[:name] =
        if filter[:name] == 'Price'
          PRICE_RANGE[f.value][@country_code]
        elsif filter[:name] == 'Discount Percent'
          "#{f.value.first.round}% - #{f.value.last.round}%"
        elsif filter[:name] == 'Rating'
          unless count.nil?
            item[:count] = count
            count -= f.count
          end
          (v = f.value.first.to_i) == 0 ? "All Products" : v == 5 ? "ignore_me" : "#{v}+"
        end
      items << item unless item[:name] == 'ignore_me'
    end
    items.compact
  end

  def id_filter_values(results)
    filter_values = []
    category_name = @category.try(:name)
    FILTERS[:id].each do |filter|
      data = filter_values(filter, results, :id)
      if filter[:group_key].blank?
        next if hidden_facet?(category_name, filter[:name])
        filter_values << {name: filter[:name], type: "id_filters", key: filter[:opt_key],
           list: data[:values].collect {|value|
             {value: value.id, name: value.send(filter[:name_key]),
               count: data[:counts][value.id]}}.sort_by!{ |k| k[:count] }.reverse,
           position: filter[:position]}
      else
        data[:values].each do |id, values|
          filter_entity = values.first.send(filter[:relation])
          next if hidden_facet?(category_name, filter_entity.name)
          facets_for_category = values.first.is_a?(PropertyValue) && FACETED_URL_KINDS[category_name]
          if @option_types.nil? || @properties.nil? || @option_types.include?(filter_entity.p_name) ||
             facets_for_category || @properties.include?(filter_entity.p_name)
             filter_values << {name: filter_entity.p_name.split.map(&:capitalize)*' ',
               type: "id_filters",
               prop_name: filter_entity.name, key: filter[:opt_key],
               priority: facets_for_category ? filter_entity.facet_priority : nil,
               list: values.collect {|value|
                 { value: value.id, name: value.send(filter[:name_key]),value_position: value.position || nil, pv_name: value.name.downcase.gsub('_','-'),
                   count: data[:counts][value.id], color_code: COLOR_CODES[value.send(filter[:name_key])]}}.sort_by { |k| [k[:value_position].nil? ? 1 : 0, k[:value_position] || 0, -k[:count]] },
                   position: filter_entity.position }
          end
        end
      end
    end
    filter_values
  end

  def get_filter_list(results)
    filter_list = []
    ready_to_ship_values = ready_to_ship_filter_values(results)
    filter_list.push(ready_to_ship_values) if ready_to_ship_values.present?
    filter_list.push(*range_filter_values(results))
    filter_list.push(*id_filter_values(results))
    gender_values = gender_filter_values()
    filter_list.push(gender_values) if gender_values.present?
    return filter_list.sort_by { |h| h[:position].to_i }
  end

  def ready_to_ship_filter_values(results)
    facet_data = results.facet(:ready_to_ship) rescue nil
    return unless facet_data
  
    ready_to_ship_count = facet_data.rows.find { |row| row.value == true }&.count || 0
    {
      name: 'Delivery Time',
      key: 'ready_to_ship',
      type: 'ready_to_ship_filter',
      list: [
        { value: 'true', name: 'Ready to Ship', count: ready_to_ship_count }
      ],
      position: RTS_FILTER_POSITION
    }
  end
  
  def cod_filter_values
    return unless @country_code == 'IN'
    {
      name: 'Only COD Products',
      key: 'pincode'
    }
  end

  def gender_filter_val
    val = []
    ['Girls','Boys'].each do |gen|
    val << {
      value: gen.downcase, name: gen }
    end
    val
  end
  
  def gender_filter_values
    unless hidden_facet?('kids','gender')
      return unless @category.try(:name).try(:downcase) == 'kids' || @category.try(:parent).try(:name).try(:downcase) == 'kids'
      {
        name: 'Gender',
        key: 'gender',
        type: "gender_filter",
        list: gender_filter_val,
        position: 5
      }
    end
  end

  def range_filter_values(results)
    Api::V1::StoreHelper::FILTERS[:range].collect do |filter|
      unless hidden_facet?(@category.try(:name), filter[:name])
        {
          name: filter[:name], keys: filter[:opt_key],
          type: "range_filter",
          list: filter_values(filter, results, :range), 
          position: filter[:position]
        }
      end
    end.compact
  end

  def get_copyright_terms
    text = ""
    COPYRIGHT_TERMS.each{ |term| text << "-'#{term}' "}
    return text
  end

  def adjust_promo_discount(discount)
    discount = discount.to_i
    if discount > 0 && discount < 100 && global_sale_in_country?
      discount = 100 - 100*(100 - discount)/(100 - global_sale_discount_percent)
    end
    discount
  end

  def hidden_facet?(kind, facet_name)
    if facet_name.present? && defined?(HIDDEN_FACETS_FILTERS)
      kind = kind.to_s.downcase
      facet_name = facet_name.downcase.gsub(/[^a-zA-Z0-9\-]|\s/, '_')
      (HIDDEN_FACETS_FILTERS['all'].to_a.include?(facet_name)) ||
      (kind.present? && HIDDEN_FACETS_FILTERS[kind].to_a.include?(facet_name))
    end
  end

  def set_seo
    desc_text = "Best Discounts & Offers on Mirraw.com"
    kind = params[:kind].try(:downcase)
    kind ||= @category.try(:name).try(:downcase)
    collection = params[:collection].try(:camelize)
    facets = params[:facets]
    tag = params[:tag]
    search_term = params[:term]
    if facets.present?
      if @colour_facets.present? && kind
        facet_and_category = "#{@colour_facets.join(' ').titleize} #{kind.titleize}"
      else
        facet_and_category = facets.titleize
      end
    end
    if params[:page].present?
      title_text = "Page #{params[:page]} of designs"
      if facets.present?
        title_text = "Page #{params[:page]} of #{facet_and_category}"
        desc_text = "Best Discounts & Offers on Mirraw #{facet_and_category}"
        keywords_text = "#{facet_and_category.downcase}, #{facet_and_category.downcase} online shopping"
      elsif kind.present?
        title_text = "Page #{params[:page]} of #{kind.camelize}"
        desc_text = "Best Discounts & Offers on Mirraw #{kind}"
        keywords_text = kind.camelize + "," + kind.camelize + " online shopping"
      elsif tag.present?
        title_text = "Page #{params[:page]} of #{tag.camelize}"
        keywords_text = tag.camelize + "," + tag.camelize + " online"
      elsif collection.present?
        title_text = "Page #{params[:page]} of designs"
        desc_text = "Best Discounts & Offers on Mirraw.com"
      elsif @designer_search.present? && @designer.name.present?
        title_text = "Page #{params[:page]} of #{@designer.name.upcase}"
      end
    elsif @seo.present?
      if facets.try(:index, 'colour-') == 0 && (@seo.label == params[:kind].try(:downcase))
        title_text = "#{facet_and_category} - Buy #{facet_and_category} Online at Best Prices"
        desc_text = "Shop stylish #{facet_and_category} at exciting discounted range from our wide collections of designer #{facet_and_category} with fast shipping worldwide and easy return process"
        keywords_text = "#{facet_and_category}, #{facet_and_category} online, designer #{facet_and_category}"
      else
        title_text = @seo.title
        desc_text = @seo.description
        keywords_text = @seo.keyword
      end
    elsif kind.present?
      if facets.present?
        if facets.try(:index, 'colour-') == 0
          title_text = "#{facet_and_category} - Buy #{facet_and_category} Online at Best Prices"
          desc_text = "Shop stylish #{facet_and_category} at exciting discounted range from our wide collections of designer #{facet_and_category} with fast shipping worldwide and easy return process"
          keywords_text = "#{facet_and_category}, #{facet_and_category} online, designer #{facet_and_category}"
        else
          title_text = "#{facet_and_category} Online Shopping for Women at Low Prices"
          desc_text = "Latest #{facet_and_category} online for women available at exciting discounted prices with free shipping in India and worldwide fast delivery for USA, UK"
          keywords_text = "#{facet_and_category}, #{facet_and_category} online"
        end
      else
        title_text = kind.camelize + "- Trending " + kind.camelize + " Designs Online"
        desc_text = "Latest #{kind.camelize} designs available from top designers of Mirraw at exciting discounted prices including low cost shipping and on time delivery"
        keywords_text = "#{kind.camelize},#{kind.camelize} designs"
      end
    elsif tag.present?
      title_text = tag.camelize + ' on Mirraw.com'
      keywords_text = tag.camelize + "," + tag.camelize + " online"
      desc_text = "Latest design of " + tag.camelize + " online shopping. Free Shipping and Hassle free returns. Call #{MIRRAW_CONTACT_INFO} for any queries."
    elsif collection.present?
      desc_text = title_text = "#{collection} Collection online"
      keywords_text = "#{collection}, #{collection} online"
    elsif @designer_search.present? and @designer.name.present?
      designer_name = @designer.name.to_s.titleize
      category_name = @designer.limited_design_categories.collect(&:name).join(', ')
      category_names = category_name.present? ? category_name.titleize : "collections"
      title_text = "#{designer_name} Designs Online Store - Shop latest #{designer_name} #{category_names} @ Best Price"
      desc_text = "Trending #{designer_name} best selling products online at Mirraw, we offer wide range of #{designer_name} designs at exciting discounted rates."
      keywords_text = "#{designer_name},#{designer_name} online store"
    elsif search_term
      title_text = "Search Results for " + search_term.camelize + " - Mirraw"
      desc_text = "Exclusive Collections of " + search_term.camelize
    else
      title_text = "Buy necklaces, earrings, pendants from designers"
    end
    @seo_title =  title_text
    @seo_description = desc_text.present? ? desc_text : title_text
    if search_term
      @seo_keywords = keywords_text
    else
      @seo_keywords = keywords_text if keywords_text.present?
    end
  end

  def generate_breadcrumb(category: nil, facet_name: nil, designer: nil)
    if category.present?
      current_category_url = nil
      breadcrumbs = category.breadcrumb_path.collect do |title, url|
        current_category_url = url
        {title: title.to_s, url: url}
      end
      if facet_name.present?
        facet_url = "#{current_category_url}/#{facet_name}"
        breadcrumbs << {title: facet_name.titleize, url: facet_url}
      end
      breadcrumbs
    elsif designer.present?
      breadcrumbs = [{title: 'Home', url: root_path}]
      breadcrumbs << {title: designer.try(:name).to_s, url: designers_path(designer)}
      breadcrumbs
    end.to_a
  end

  def search_result_title
    if params[:kind].present?
      if params[:kind] == "b1g1" && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
        Promotion.bmgnx_offer_message(bmgnx_hash)
      elsif params[:facets].present?
        if params[:facets].index('colour') == 0 && @category_title.present?
          product_title = params[:facets].partition('colour-').last.split('--').join(' ').titleize
          "#{product_title} #{@category_title}"
        else
          params[:facets].titleize
        end
      elsif @category_title.present?
        "#{@category_title.strip.camelize}"
      else
        "#{params[:kind].gsub(/-/,' ').humanize}"
      end
    elsif params[:term].present? && params[:utf8]
      "Search results for #{params[:term].humanize}"
    elsif params[:q].present? && params[:utf8].blank?
      params[:q].humanize
    elsif params[:collection].present?
      @collection_heading.titleize
    elsif params[:catalogue].present?
      @catalogue_heading.titleize
    elsif (designer_id = (params[:id] || params[:designer_id])) && designer_id.to_i > 0
      Designer.find_by_cached_slug(designer_id).name.titleize
    end
  end
end
