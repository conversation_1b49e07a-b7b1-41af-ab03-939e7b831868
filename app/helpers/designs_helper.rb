module DesignsHelper

  def get_property_data(properties, design_spec_hash, spec_group)
    design_spec_hash[:seo_keys] = {}
    properties.each do |prop|
      if prop['value'].any?
        value = prop['value'].uniq.join(',')
        if(spec = spec_group.select{|spec| prop['type'].downcase.include?(spec)}).present?
          design_spec_hash[spec[0]][prop['type'].downcase.sub(spec[0],'')] = value
        else
          design_spec_hash['other'][prop['type']] = value
        end
        if ['color', 'top color', 'saree color', 'kameez color', 'lehenga color'].include?(prop['type'].downcase)
          design_spec_hash[:seo_keys]['color'] = value
        end
      end
    end
  end

  def get_designable_data(designable, design_spec_hash, spec_group)
    designable.each do |key|
      if key['value'].present?
        key['type'] = key['type'] == 'blouse_image' ? 'blouse as per image' : key['type']
        key['value'] = key['value'] == true ? 'Yes' : key['value']
        if(spec = spec_group.select{|spec| key['type'].include?(spec)}).present?
          design_spec_hash[spec[0]][key['type'].sub(spec[0],'')] = key['value']
        else
          design_spec_hash['other'][key['type']] = key['value']
        end
      end
    end
  end

  def get_spec_data(specifications, type, spec_group=[])
    design_spec_hash = {}
    spec_group.each do |spec|
      design_spec_hash[spec] = {}
    end
    design_spec_hash['other'] = {'Product ID'=>specifications['product_id'], 'Package Details'=> specifications['package_details'], 'Type'=>type, 'Region'=> specifications['region'], 'Weight'=> specifications['weight']}
    get_property_data(specifications['properties'], design_spec_hash, spec_group) if specifications['properties'].present?
    get_designable_data(specifications['designable'], design_spec_hash, spec_group) if specifications['designable'].present?
    return design_spec_hash
  end

  def get_key_spec_data(design_spec_hash)
    Design::DESIGN_KEY_SPEC_SUB_GROUP[@design['designable_type'].to_s].to_a.collect do |spec, properties|
      property_hash = properties.collect do |property|
        property_value = design_spec_hash[spec].to_h.find{|p_name, p_value| p_name.downcase.include?(property)}.try(:second)
        [property, property_value] if property_value.present?
      end.compact.to_h
      [spec, property_hash] if property_hash.present?
    end.compact.to_h
  end

  def get_standard_images
    ['knee', 'calf', 'ankle'].map do |img|
      image_tag "#{img}.jpg", id:"std-#{img}", style: 'display:block;', height: 250 #dont change order of attributes else js won't work
    end.join('')
  end
  
  def uk_size_for_footwear(parent_id,id)
    size = "Select Size"
    if FOOTWEAR_CATEGORY_PARENT_IDS.include?(parent_id) || FOOTWEAR_CATEGORY_IDS.include?(id)
      size+=" (UK Size)"
    end
    return size
  end

  def eligible_for_free_shipping(discount_price)
    country_currency = CurrencyConvert.currency_convert_cache_by_country_name(@actual_country)
    on_design_price = Promotion.get_free_shipping(country_currency.country, true)
    return on_design_price.present? && on_design_price.first > 0 && discount_price.round(0) >= (on_design_price.first/country_currency.rate)
  end

  def product_offer_free_shippable?(product_offer=nil)
    free_shippable = true
    free_shippable = false if product_offer.present? && ['flash_deals', 'bmgnx'].include?(product_offer["type"])
    return free_shippable
  end

  def check_addon_type(type_value)
    case type_value
      when 'shapewear'
        return 'shapewear'
      when 'petticoat stitching'
        return 'petticoat_stitching_div'
    end
  end

  def custom_select(index, option_value)
    content_tag(:select, id: "atov_#{index}") do
      options = content_tag(:option, option_value['p_name'], value: '0')
      option_value['addon_option_values'].each do |addon_option|
        options << content_tag(:option, addon_option['p_name'], value: addon_option['id'], data: { prodtime: '' })
      end
      options
    end
  end

end
