class Api::V2::CartsController < ApiController
  include ApplicationHelper
  before_action :set_min_cart_value, only: [:payment_details]
  before_action :set_cart, only: [:payment_details]


  ASSOCIATIONS = Api::V1::CartsController::ASSOCIATIONS

  def payment_details
    enable_cod = true
    if @country_code == 'IN' && GOKWIK_CONFIG['enable_gk_on_api']
      enable_cod = call_gokwik_rto_api
    end

    @details = @cart.payment_details_v2(
      payment_details_params.merge(enable_cod: enable_cod),
      @rate,
      @min_cart_value,
      @app_source.downcase,
      @country_code,
      request.headers['App-Version'],
      request.headers['Sub-App'],
      subscription_params.presence || { subscription_applied: "false" }
    )

    @juspay_enable = @country_code == 'IN' && payment_details_params[:billing][:country] == 'India' && rand(1..ENV['JUSPAY_EXPRESS_CHECKOUT'].to_i) == 1
    @payment_offers = OfferPanel.active_domestic_offers(@app_source.downcase) if @juspay_enable
  end

  def call_gokwik_rto_api
    res = get_response_data_for('gokwiks_predict_rto_risk', params.merge({request_method: "POST",
      cart_id: current_cart.id,
      session: {
      utm_medium: session[:utm_medium], utm_source: session[:utm_source], utm_campaign: session[:utm_campaign],
      remote_ip: request.remote_ip, user_agent: request.user_agent
    }}))
    cod_enable = (res["data"].present? ? (GOKWIK_CONFIG["risk_flags"].include?(res["data"]["risk_flag"].downcase) ? false : true) : true) if !GOKWIK_CONFIG['enable_cod']
  end

  def subscription_params
    params.fetch(:subscription, {}).permit(:subscription_applied)
  end

  private

  def payment_details_params
    params.require(:location).permit(
      billing: [:pincode, :country, :id],
      shipping: [:pincode, :country, :id]
    )
  end

  def set_min_cart_value
    @min_cart_value = INTERNATIONAL_MIN_CART_VAL.to_i
  end

  # will move this and v1 method in Helper Later
  def set_cart
    @cart = current_cart(ASSOCIATIONS)
    @cart.remove_existing_invalid_coupon
    @messages, @imp_msg = @cart.get_messages(@rate, @hex_symbol, @country_code, @actual_country, @app_source.split('-',2)[0], current_user, @min_cart_value, request.headers['App-Version'], @shipping_country)
    set_cart_promotion
  end
end
