# == Schema Information
#
# Table name: addon_type_values
#
#  id                        :integer          not null, primary key
#  name                      :string(255)
#  position                  :integer
#  price                     :integer
#  description               :text
#  design_id                 :integer
#  addon_type_id             :integer
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  master_id                 :integer
#  published                 :boolean
#  prod_time                 :integer
#  visible_for               :string(255)      default("all"), not null
#  payable_to                :string(255)      default("mirraw"), not null
#  addon_type_value_group_id :integer
#  db_name                   :string(255)
#

class AddonTypeValue < ActiveRecord::Base
  belongs_to :design
  belongs_to :addon_type
  has_many :addon_option_values
  has_many :addon_option_types, through: :addon_option_values
  has_many :line_item_addons
  belongs_to :addon_type_value_group

  # Attributes that require currency conversion
  # Needs to always be above Priceable
  PRICE_ATTR = [:price]

  include Priceable

  def self.get_addon_type_value_to_display_stiching
    addon_value_types = AddonTypeValue.where('design_id IS NULL')
    atv_prices = {}
    addon_value_types.each do |atv|
      if atv.description  == 'addons_lehenga'
        atv_prices[:atv_lehenga_std] =  atv.price.to_i if atv.name == 'Standard Stitching'
        atv_prices[:atv_lehenga_custom] =  atv.price.to_i if atv.name == 'Custom Stitching'
      elsif atv.description  == 'addons_salwar'
        atv_prices[:atv_salwar_suit_std] =  atv.price.to_i if atv.name == 'Standard Stitching'
        atv_prices[:atv_salwar_suit_custom]=  atv.price.to_i if atv.name == 'Custom Stitching'
      elsif atv.description  == 'addons_blouse'
        atv_prices[:atv_blouse_std] =  atv.price.to_i if atv.name == 'Regular Blouse Stitching'
        atv_prices[:atv_blouse_custom] =  atv.price.to_i if atv.name == 'Custom Blouse Stitching'
      elsif atv.description  == 'addons_saree'
        atv_prices[:atv_fnp] = atv.price.to_i if atv.name == 'Fall and Pico'
        atv_prices[:atv_petticoat] = atv.price.to_i if atv.name == 'Petticoat Stitching'
      end
    end
    atv_prices
  end

  def get_addon_option_types(app_source, app_version, type)
    (AddonTypeValue.is_eligible_for_checkbox_addon_types(app_source, app_version, type) ? self.addon_option_values : self.addon_option_values.select{|aot| ['select', 'radio'].include?(aot.addon_option_type.option_type)}).group_by(&:addon_option_type_id)
  end

  def self.is_eligible_for_checkbox_addon_types(app_source, app_version, type)
    ['mobile','desktop'].include?(app_source.downcase) || (type == 'Lehenga' && ((app_source.include?('android') && ALLOWED_APP_VERSIONS[67..-1].include?(app_version)) || (app_source.include?('ios') && ALLOWED_IOS_APP_VERSIONS[21..-1].include?(app_version))))
  end
end
