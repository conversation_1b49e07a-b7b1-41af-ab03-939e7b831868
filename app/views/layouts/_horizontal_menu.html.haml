- unless checkout_flow_page? || checkout_cart_login? || (controller_name == "registrations" && action_name == "create_guest")
  %nav.tab-bar
    - unless check_for_alternate_tab?
      -# if params['action'] == 'home'
      %section.left-small
        .menu-icon
          %span
      -# - else
      -#   %section.back-button#back-button
      -#     %a.left-arrow
    %section.right.tab-bar-section
      .left.medium-4.small-5.columns{style: ('margin-left: -56px' if check_for_alternate_tab?)}
        = link_to '', '/', class: 'logo left', 'aria-label' => 'Logo'
      .right.medium-3.small-2.columns.nopadding.user-cart-icon
        = link_to cart_path, data: {turbolinks: 'false'} do
          - if !opera_mini_browser?
            .right
              %span.secondary.round.tiny.cart_count{data: {turbolinks_permanent: ''}}
                = "#{session[:cart_count]}"
          - else
            .right.secondary.round.tiny.cart_count{data: {turbolinks_permanent: ''}}
              = "#{session[:cart_count]}"
          .right{style: "margin-top: 0.3rem;margin-right: 0.7rem;"}
            .cart-icon
      - if account_signed_in?
        .right.medium-1.small-1.columns.user-wishlist.nopadding.user-wishlist-icon
          = link_to user_wishlists_path do
            %i.fi-heart
      .right.medium-4.small-4.columns.nopadding
        - if is_android?      
          = link_to "https://play.google.com/store/apps/details?id=com.mirraw.android&utm_source=download_app&utm_medium=mirraw_mobile_web", target: "_blank", rel: "noopener" do
            .download-app.right Get App
            .app-download.right
        - elsif browser.platform.ios?
          = link_to "https://itunes.apple.com/app/apple-store/id1112569519?pt=*********&ct=mobile_header_button&mt=8", target: "_blank", rel: "noopener" do
            .download-app.right Get App
            .app-download.right
  .search-tab.tab-bar{style: "height: #{has_search_bar? ? '6.3125rem' : '2.15rem'};"}
    - unless check_for_alternate_tab?
      = render partial: '/layouts/tabs'
      - unless design_details_page?
        - if return_details_page?
          = render partial: 'layouts/order_return_tab'
        - elsif has_search_bar?
          %span.show-for-small-up.search_margin
            = render partial:  'layouts/search_form'
    - else
      = render partial: '/layouts/tabs'
- else
  - if !login_page?
    %nav.tab-bar
      -# %section.back-button#back-button
      -#   %a.left-arrow
      %section.tab-bar-section
        .small-3.columns.search-box-margin
          = link_to '', '/', class: 'logo left'
    -if return_details_page?
      .tab-bar
        = render partial: 'layouts/order_return_tab'
-if controller_name == 'pages' &&  action_name == 'home'
  .custom_deal_timer
    = render 'layouts/campaign_timer'
-# :javascript
  
-#   var backButton = document.getElementById("back-button");
-#   var backButton2 = document.getElementById("back-button-2");
  
-#   if (backButton) {
-#     backButton.addEventListener("click", goBackTo);
-#   }
-#   else if (backButton2) {
-#     backButton2.addEventListener("click", goBackTo);
-#   }

-#   function goBackTo() {
-#     if (document.referrer.match(/(mirraw)/gm) === null) {
-#       return window.location = "/";
-#     } else {
-#       return window.history.back();
-#     }
-#   }