-paypal_allowed_currencies = Order::PAYPAL_ALLOWED_CURRENCIES
-currency_symbol = paypal_allowed_currencies.exclude?(@symbol)? 'USD' : @symbol
%script{:src => "https://www.paypal.com/sdk/js?client-id=#{ENV['PAYPAL_CLIENT_ID']}&enable-funding=paylater&disable-funding=ideal&commit=true&currency=#{currency_symbol}&components=messages,buttons"}
= javascript_include_tag 'paypal_client.js'
.paypal-smart-payment-buttons(style="position: relative; z-index: 1;")
  #paypal-button-container{{style: 'padding: 30px'}}
