.row
  .bordered_block.columns
    .row.shipping_options
      = 'Shipping Options :'
      - delivery_message = 'Get your order delivered within '
      - delivery_days = @cart.get_delivery_time(@shipping_address.try(:country), @shipping_address.try(:city))[0].to_i
      - delivery_days = (is_domestic? ? DOMESTIC_SHIPPING_TIME : INTERNATIONAL_SHIPPING_TIME) if delivery_days == 0
      .row.shipping_option
        .small-8.medium-8.large-8.columns.shipping_radio_button
          %label
            - regular_shipping_price = get_price_with_symbol(shipping, @symbol)
            - total_regular_delivery = get_price_with_symbol(grandtotal, @symbol)
            = radio_button_tag(:delivery_type, 'regular', true, data: {shipping: regular_shipping_price, total: total_regular_delivery, delivery_days: delivery_days})
            = 'Regular Shipping'
        #regular_shipping.right.shipping_option_price
          = shipping > 0 ? regular_shipping_price : 'FREE'
      .row
        .delivery_message #{delivery_message} #{delivery_days} days
      #express_delivery_division
        .row.shipping_option
          .small-8.medium-8.large-8.columns.shipping_radio_button
            %label
              - express_shipping_price = get_price_with_symbol(shipping + get_price_in_currency(express_shipping), @symbol)
              - total_express_delivery = get_price_with_symbol(grandtotal + get_price_in_currency(express_shipping), @symbol)
              = radio_button_tag(:delivery_type, 'express', false, data: {extraa_shipping: express_shipping, shipping: express_shipping_price, total: total_express_delivery, delivery_days: READY_TO_SHIP_DESIGNS.to_i + 1})
              = 'Express Shipping'
          #express_shipping.right.shipping_option_price
            = express_shipping_price
        .row
          .delivery_message
            = "#{delivery_message} #{READY_TO_SHIP_DESIGNS.to_i + 1} days"
      #express_delievery_cannot_be_applied_for_wallet{style:"display:none;"}
        Express delivery can not be applied for Wallet.