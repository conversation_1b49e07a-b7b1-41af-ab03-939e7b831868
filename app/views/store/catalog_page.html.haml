= render partial: 'seo'
= render 'layouts/flash_msg'

- if params[:kind].present?
  - if params[:kind] == "b1g1" && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
    - product_title = Promotion.bmgnx_offer_message(bmgnx_hash)
  - elsif params[:facets].present?
    - if params[:facets].index('colour') == 0 && @category_title.present?
      - product_title = params[:facets].partition('colour-').last.split('--').join(' ').titleize
      - product_title = "#{product_title} #{@category_title}"
    - else
      - product_title = params[:facets].titleize
  - elsif @category_title.present?
    - product_title = "#{@category_title.strip.camelize}"
  - else
    - product_title = "#{params[:kind].gsub(/-/,' ').humanize}"
- elsif params[:term].present? && params[:utf8]
  - product_title = "Search results for #{params[:term].humanize}"
- elsif params[:q].present? && params[:utf8].blank?
  - product_title = params[:q].humanize
- elsif params[:collection].present?
  - product_title = @collection_heading.titleize
- elsif params[:catalogue].present?
  - product_title = @catalogue_heading.titleize
- elsif params[:id].present?
  - product_title = params[:id].titleize
- currency_symbol = get_symbol_from(@hex_symbol)
- if @store_page.present?
  .row
    - if !is_mobile_view?
      .large-3.small-3.columns.flex--item-1.sticky-element.desk_web.filter_side.filter-box
        .store-breadcrumb
          = render partial: 'sitemaps/breadcrumb', locals: {breadcrumb: @breadcrumbs}
        - if @country_code == 'IN' && params.has_key?(:q)
          = render partial: 'side_filter_unbxd'
        - else
          = render partial: 'side_filter'
    .large-9.small-9.columns.store_page_block
      .heading_title.row
        - if params[:q].present? && params[:utf8].blank?
          = hidden_field_tag 'tag_list', params[:q]
        - if @store_page['uuid']
          = hidden_field_tag 'uuid', @store_page['uuid']
        -if is_mobile_view?
          .store-breadcrumb
            = render partial: 'sitemaps/breadcrumb', locals: {breadcrumb: @breadcrumbs}
        - if is_mobile_view?
          .product-title-mobile.columns
            %h1.product-name.product-name-mobile
              = product_title
            %span.product_count_mobile
              = @store_page['results']
              Items
          -if render_seo_top_content?
            .add-read-more.show-less-content=raw @seo.top_content
        - else
          .product-title.columns
            %h1= product_title
            -if render_seo_top_content?
              .add-read-more.show-less-content=raw @seo.top_content
            .product_count
              = @store_page['results']
              Items
      .category-sort-container{class: @category_links.present? ? "justify-between" : "justify-end"}
        - if @category_links.present?
          #category_links
            .link-container
              - if @category_links.new_arrival.present?
                %a.button.tiny.custom.success{:href => @category_links.new_arrival} New Arrivals
              - if @category_links.best_seller.present?
                %a.button.tiny.custom.success{:href => @category_links.best_seller} Best Sellers
              - if @category_links.best_discount.present?
                %a.button.tiny.custom.success{:href => @category_links.best_discount} Ready To Ship
              - if @category_links.others.present? && @category_links.others.is_a?(Hash)
                - other_links = @category_links.others
                - other_links.each do |key, value|
                  %a.button.tiny.custom.success{:href => value}= key.humanize.titleize
        .sort-container
          .new_box.sort-by-wrap.small-4.columns.desk_web
            %span.sort-by-txt SORT BY:
            #custom_sort#sortByDropdownMenuBox.dropdown
              - if @country_code == 'IN' && params.has_key?(:q)
                - select_default = ['HandPicked']
                = select_tag 'sort', options_for_select( ApplicationHelper::UNBXD_SORT, params[:sort] || select_default), class: 'select_sort_value', 'aria-label'=> 'sort input'
              - elsif @country_code == 'IN'
                = select_tag 'sort', options_for_select(ApplicationHelper::SORT_TYPE.to_a, ApplicationHelper::SORT_TYPE[Api::V1::StoreHelper.domestic_sorts[(params[:sort].presence || 7).to_s][:name]]), class: 'select_sort_value', 'aria-label'=> 'sort input'
              - else
                = select_tag 'sort', options_for_select(ApplicationHelper::SORT_TYPE.to_a, ApplicationHelper::SORT_TYPE[Api::V1::StoreHelper.international_sorts[(params[:sort].presence || 7).to_s][:name]]), class: 'select_sort_value', 'aria-label'=> 'sort input'
      - if !is_mobile_view?
        #filter-chip-box.row
          .col-lg-12.col-md-12.col-sm-12.hidden-xs.nopadding
            #filter-chips
              #clear-all-btn
                %a.clear_all_filters{href: "javascript:void(0)"} Clear All Filters &#10006;
      / %hr{style: "margin: 8px 0px;"}
      - if @store_page['designs'].present?
        %span{:itemprop => "WebPage", :itemscope => "", :itemtype => "http://schema.org/WebPage"}
          - base_url = request.url.split('?').first
          %span{:itemprop => "url", :content => base_url}
          %span{:itemprop => "mainEntity", :itemscope => "", :itemtype => "http://schema.org/OfferCatalog"}
            -if product_title ||= false
              %span{:itemprop => "name", :content => product_title}
            %span{:itemprop => "url", :content => base_url}
            %span{:itemprop => "numberOfItems", :content => @store_page['designs'].count}
            %ul.store_page_design.small-block-grid-2.medium-block-grid-4.large-block-grid-4
              = render partial: 'designs', locals: {store_designs: @store_page['designs']}
      .current_page{data: {page: (params[:page] || 1)}}
      .navigate_page.text-center.li_append
        - if @store_page['previous_page'].present?
          %a.button.secondary.nav-button.tiny.previous{:id => "page_#{@store_page['previous_page']}", :href => "#" }
            = 'Previous'
        - if @store_page['next_page'].present?
          %a.button.nav-button.tiny.next{:id => "page_#{@store_page['next_page']}", :href => "#" }
            = 'Next'
        #more-designs-loader
      - unless check_for_alternate_tab?
        .sort_filter_options
          .position_div
          #action_buttons.sort_filter_button
            .action_button_btn
            -if ENABLE_WIZGO["enable"] == true
              = render partial: '/layouts/whatsapp_partial', locals: {text: "Hi! Could you help me with a few queries!"}
            - unless @store_page.blank?
              .plp_dealtimer
                = render 'layouts/campaign_timer'
              %ul.small-block-grid-2.medium-block-grid-2.sorting-button
                %li.action_button#short_btn
                  .select_box
                    .button.secondary.small#custom_sort
                      %i.fi-arrow-up
                      %i.fi-arrow-down
                      SORT
                    - if @country_code == 'IN' && params.has_key?(:q)
                      - select_default = ['HandPicked']
                      = select_tag 'sort', options_for_select( ApplicationHelper::UNBXD_SORT, params[:sort] || select_default), class: 'form_input_m_select', 'aria-label'=> 'sort input'
                    - elsif @country_code == 'IN'
                      = select_tag 'sort', options_for_select(ApplicationHelper::SORT_TYPE.to_a, ApplicationHelper::SORT_TYPE[Api::V1::StoreHelper.domestic_sorts[(params[:sort].presence || 7).to_s][:name]]), class: 'form_input_m_select', 'aria-label'=> 'sort input'
                    - else
                      = select_tag 'sort', options_for_select(ApplicationHelper::SORT_TYPE.to_a, ApplicationHelper::SORT_TYPE[Api::V1::StoreHelper.international_sorts[(params[:sort].presence || 7).to_s][:name]]), class: 'form_input_m_select', 'aria-label'=> 'sort input'
                %li.action_button#filter_btn
                  #filter-button.button.secondary{class: ('filter-border' if cookies[:theme] == 'black_theme'), "data-reveal-id" => "filterModal"}
                    %i.fi-filter
                    FILTER
          - unless @store_page.blank?
            #filterModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px; width: 100% !important;'}
              - if @country_code == 'IN' && params.has_key?(:q)
                = render partial: 'facet_sort_unbxd'
              - else
                = render partial: 'facet_sort'
              %a.close-reveal-modal
              #loader
        #back-top.float-Btn
          %i.fi-arrow-up
        %input#spaceid{style: "display:none;", name: "spaceid", type: "hidden", value: "0002319000200200"}
      .page_view_port
- else
  .store_page_block
    .store-breadcrumb
      = render partial: 'sitemaps/breadcrumb', locals: {breadcrumb: @breadcrumbs}
    %h1.product-title= product_title
    %h2.text-center.no-design-found
      No Designs Found
-unless params[:page].present?
  =render partial: '/store/seo_post'
  =render partial: '/store/faqs', locals: {faqs: @faqs} if @faqs.present?
=render partial: '/store/seo_popular_search' if @popular_links.present?

-unless firefox_browser?
  - content_for :page_specific_css do
    = stylesheet_link_tag 'catalog', rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"

- content_for :page_specific_js do
  = #javascript_include_tag "store"

  - if firefox_browser?
    = async_stylesheet_tag('catalog')
  :javascript
    afterWindowOrTrubolinksLoad(function(){loadScript('#{asset_url("store.js")}')})

 
- if @search.present?
  - content_for :head do
    :javascript
      branchData = {
        '$deeplink_path': "search?term=#{@search}",
        'term':"#{@search}",
        '~campaign': "web_search_#{@search}"
      }
      
      UnbxdAnalyticsConf=window.UnbxdAnalyticsConf ||{};
      UnbxdAnalyticsConf["query"]="#{@search}";

- if @category_parent_id.present?
  - content_for :head do
    :javascript
      branchData = {
        '$deeplink_path': "listing?key=category_parent_id&value=#{@category_parent_id}&title=#{product_title}",
        'key':'category_parent_id',
        'value':"#{@category_parent_id}",
        'title':"#{product_title}",
        '~campaign':"web_category_#{@category_parent_id}"
      }

- if @collection.present?
  - content_for :head do
    :javascript
      branchData = {
        '$deeplink_path': "listing?key=collection&value=#{@collection}&title=#{product_title}",
        'key':'collection',
        'value':"#{@collection}",
        'title':"#{product_title}",
        '~campaign':"web_collection_#{@collection}"
      }
- content_for :head do
  %link{href: "android-app://com.mirraw.android/mirraw#{request.fullpath}", rel: "alternate"}/
  %link{href: "ios-app://1112569519/mirraw:#{request.fullpath}", rel: "alternate"}

:javascript
  window.dataLayer = window.dataLayer || [];
  var ga4_catalog_params = #{@ga_hash_new.to_json};
  var gads_items_id = #{@googe_add_hash_new.to_json};
  dataLayer.push({ ecommerce: null });
  dataLayer.push(ga4_catalog_params)

- if !is_mobile_view? 
  :javascript
    afterWindowOrTrubolinksLoad(function(){
      if (MR.accordionSearch == undefined){
        loadAsyncScripts('#{asset_url("catalog/catalog.js")}', function(){
        MR.accordionSearch.init()
        MR.store.init()
        })
      }
      else{
        MR.accordionSearch.init()
        MR.store.init()
        }
      })
            