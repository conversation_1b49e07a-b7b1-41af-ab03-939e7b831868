# script to change configuration
# config = {
#     "creditdebit_cardnet_banking" => {
#         "int_position" => 1,
#           "int_enable" => true,
#         "dom_position" => 1,
#           "dom_enable" => true
#     },
#     "paytm" => {
#         "int_position" => 2,
#           "int_enable" => true,
#         "dom_position" => 2,
#           "dom_enable" => true
#     },
#     "paypal" => {
#         "int_position" => 3,
#           "int_enable" => true,
#         "dom_position" => 3,
#           "dom_enable" => true
#     },
#     "cash_on_delivery" => {
#         "int_position" => 4,
#           "int_enable" => true,
#         "dom_position" => 4,
#           "dom_enable" => true
#     },
#     "bank_deposit" => {
#         "int_position" => 4,
#           "int_enable" => true,
#         "dom_position" => 5,
#           "dom_enable" => false
#     }
# }
# st = SystemConstant.where(name: 'PAYMENT_OPTION').first_or_create
# st.value = config.to_json
# st.value_type = 'json'
# st.save


# <% constant = Hash.new {|h,k| h[k]= Hash.new {|h,k| h[k]= {}}} if !defined?(constant) %>
# <% constant.default_proc =  proc {|h,k| h[k]= {} } %>
# Credit/Debit Card/Net Banking:
#   int_position: <%= constant['creditdebit_cardnet_banking']['int_position'] || 1 %>
#   int_enable: <%= constant['creditdebit_cardnet_banking']['int_enable'] == false ? false : true %>
#   dom_position: <%= constant['creditdebit_cardnet_banking']['dom_position'] || 1 %>
#   dom_enable: <%= constant['creditdebit_cardnet_banking']['dom_enable'] == false ? false : true %>
Paytm:
  int_position: <%= constant['paytm']['int_position'] || 1 %>
  int_enable: <%= constant['paytm']['int_enable'] == false ? false : true %>
  dom_position: <%= constant['paytm']['dom_position'] || 1 %>
  dom_enable: <%= constant['paytm']['dom_enable'] == false ? false : true %>
Cash On Delivery:
  int_position: <%= constant['cash_on_delivery']['int_position'] || 1 %>
  int_enable: <%= constant['cash_on_delivery']['int_enable'] == false ? false : true %>
  dom_position: <%= constant['cash_on_delivery']['dom_position'] || 1 %>
  dom_enable: <%= constant['cash_on_delivery']['dom_enable'] == false ? false : true %>
Bank Deposit:
  int_position: <%= constant['bank_deposit']['int_position'] || 1 %>
  int_enable: <%= constant['bank_deposit']['int_enable'] == false ? false : true %>
  dom_position: <%= constant['bank_deposit']['dom_position'] || 1 %>
  dom_enable: <%= constant['bank_deposit']['dom_enable'] == false ? false : true %>
Pay Online:
  int_position: <%= constant['paypal']['int_position'] || 1 %>
  int_enable: <%= constant['paypal']['int_enable'] == false ? false : true %>
  dom_position: <%= constant['paypal']['dom_position'] || 1 %>
  dom_enable: <%= constant['paypal']['dom_enable'] == false ? false : true %>  
